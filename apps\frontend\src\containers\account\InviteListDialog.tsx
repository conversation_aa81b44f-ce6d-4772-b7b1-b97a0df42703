import { Loading, Text } from "@snap/design-system";
import { X, Mail, CheckIcon, MailWarning, MailX, AlertTriangle } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { Column, DataTable } from "~/components/Table";
import { useUserCRUD } from "~/hooks/useUserCRUD";
import { useDialogActions, useIsDialogIsOpen } from "~/store/dialogStore";
import { useUserInviteList, useUserInviteListActions } from "~/store/userInviteListStore";
import { useUserData } from "~/store/userStore";
import { UserInviteResponse } from "~/types/global";
import { USER_CONSTANTS } from "~/helpers/constants";
import { usePermissionCheck } from "~/components/router/PermissionGuard";
import { Permission } from "~/helpers/permissions.helper";
import { OrganizationChangeDialog } from "./OrganizationChangeDialog";
import { LogoutMessageDialog } from "./LogoutMessageDialog";
// import { ENABLE_MOCKS, mockInvites, mockAnswerInviteMutation, mockUserData } from "./__mocks__/inviteListMocks";

interface InviteListDialogContentProps { }

const columnProps = {
  invite_id: USER_CONSTANTS.user_invite_list.invite_id,
  status_invite: USER_CONSTANTS.user_invite_list.status_invite,
  user_sender_id: USER_CONSTANTS.user_invite_list.user_sender_id,
  name_sender: USER_CONSTANTS.user_invite_list.name_sender,
  email_sender: USER_CONSTANTS.user_invite_list.email_sender,
  organization_name: USER_CONSTANTS.user_invite_list.organization_name,
}

const getStatusIcon = (status: string | undefined) => {
  switch (status) {
    case "enviado":
      return <MailWarning className="size-5 text-secondary" />
    case "negado":
      return <MailX className="size-5 text-accent" />;
    default:
      return <Mail className="size-4 text-secondary" />;
  }
};

const getStatusText = (status: string | undefined) => {
  switch (status) {
    case "enviado":
      return "Pendente";
    case "negado":
      return "Negado";
    default:
      return status || "Desconhecido";
  }
};

const renderTextStyleFromStatus = (status: string | undefined) => {
  switch (status) {
    case "enviado":
      return "font-semibold";
    case "negado":
      return "font-normal opacity-50";
    default:
      return "font-normal";
  }
};

const inviteColumns: Column<UserInviteResponse>[] = [
  {
    key: columnProps.status_invite,
    header: "Status",
    widthClass: "w-1/4 min-w-[120px]",
    render: (_, row: UserInviteResponse) => (
      <div className="flex items-center gap-2">
        {getStatusIcon(row.status_invite)}
        <Text className={renderTextStyleFromStatus(row.status_invite)}>
          {getStatusText(row.status_invite)}
        </Text>
      </div>
    ),
  },
  {
    key: columnProps.email_sender,
    header: "Enviado por",
    widthClass: "w-1/4 min-w-[120px]",
    render: (_, row: UserInviteResponse) => (
      <div className="flex items-center gap-2">
        <Text className={renderTextStyleFromStatus(row.status_invite)}>
          {row.email_sender || "N/A"}
        </Text>
      </div>
    ),
  },
  {
    key: columnProps.name_sender,
    header: "Nome",
    widthClass: "w-1/4 min-w-[120px]",
    render: (_, row: UserInviteResponse) => (
      <div className="flex items-center gap-4">
        <Text className={renderTextStyleFromStatus(row.status_invite)}>
          {row.name_sender || "N/A"}
        </Text>
      </div>
    ),
  },
  {
    key: columnProps.organization_name,
    header: "Organização",
    widthClass: "w-1/4 min-w-[120px]",
    render: (_, row: UserInviteResponse) => (
      <div className="flex items-center gap-4">
        <Text className={renderTextStyleFromStatus(row.status_invite)}>
          {row.organization_name || "N/A"}
        </Text>
      </div>
    ),
  },
];


export function InviteListDialogContent({ }: InviteListDialogContentProps) {
  const userInviteList = useUserInviteList();
  const { setUserInviteList } = useUserInviteListActions();
  const { userInviteQuery, answerInviteMutation, acceptInviteMutation, invalidadeUserInvite } = useUserCRUD();
  const { closeDialog, openDialog } = useDialogActions();
  const isOpen = useIsDialogIsOpen();
  const { data: invitesData, isFetching, refetch } = userInviteQuery("enviado");
  const userData = useUserData();
  const [isAcceptingInvite, setIsAcceptingInvite] = useState(false);

  // TODO - mocks - remover lógica e pasta de mocks quando não for mais necessário
  // const actualUserData = ENABLE_MOCKS ? mockUserData : userData;
  // const actualAnswerInviteMutation = ENABLE_MOCKS ? mockAnswerInviteMutation : answerInviteMutation;
  // const actualInvitesData = ENABLE_MOCKS ? mockInvites : invitesData;

  useEffect(() => {
    if (invitesData) {
      setUserInviteList(invitesData);
    }
  }, [invitesData, setUserInviteList]);

  useEffect(() => {
    // TODO - mudar lógica para não fazer a requisição duas vezes /não precisar do refetch
    if (isOpen) {
      refetch();
    }
  }, [isOpen]);

  const handleAcceptInvite = (invite: UserInviteResponse) => {
    if (!invite.invite_id) {
      toast.error("ID do convite não encontrado");
      return;
    }

    const currentOrganization = userData?.organization_name;
    const inviteOrganization = invite.organization_name;

    const handleConfirm = () => {
      setIsAcceptingInvite(true);
      const loadingToast = toast.loading("Processando convite...", {
        description: "Você será redirecionado para a página de login."
      });

      acceptInviteMutation.mutate({
        data: {
          accept_invite: true,
          invite_id: invite.invite_id!,
        },
        loadingToast
      }, {
        onSettled: () => {
          setIsAcceptingInvite(false);
        }
      });
    };

    const handleCancel = () => {
      openDialog({
        title: "Convites",
        icon: <Mail />,
        content: <InviteListDialogContent />,
        className: "max-w-4xl",
      });
    };

    if (currentOrganization && inviteOrganization && currentOrganization !== inviteOrganization) {
      openDialog({
        title: "Mudança de Organização",
        icon: <AlertTriangle />,
        content: (
          <OrganizationChangeDialog.Content
            currentOrganization={currentOrganization}
            newOrganization={inviteOrganization}
            onConfirm={handleConfirm}
            onCancel={handleCancel}
          />
        ),
        footer: (
          <OrganizationChangeDialog.Footer
            onConfirm={handleConfirm}
            onCancel={handleCancel}
            isLoading={isAcceptingInvite}
          />
        ),
        className: "max-w-lg",
      });
    } else {
      openDialog({
        title: "Confirmação de aceite",
        icon: <AlertTriangle />,
        content: (
          <LogoutMessageDialog.Content
            onConfirm={handleConfirm}
            onCancel={handleCancel}
          />
        ),
        footer: (
          <LogoutMessageDialog.Footer
            onConfirm={handleConfirm}
            onCancel={handleCancel}
            isLoading={isAcceptingInvite}
          />
        ),
        className: "max-w-lg",
      });
    }
  };

  const handleRejectInvite = (invite: UserInviteResponse) => {
    if (!invite.invite_id) {
      toast.error("ID do convite não encontrado");
      return;
    }

    answerInviteMutation.mutate({
      accept_invite: false,
      invite_id: invite.invite_id,
    }, {
      onSuccess: async () => {
        toast.success("Convite rejeitado com sucesso!");
        await invalidadeUserInvite();
      }
    });
  };


  const inviteActions = [
    {
      label: "Aceitar",
      onClick: handleAcceptInvite,
      icon: <CheckIcon />,
      disabled: answerInviteMutation.isPending
    },
    {
      label: "Rejeitar",
      onClick: handleRejectInvite,
      icon: <X />,
      disabled: answerInviteMutation.isPending
    }
  ];

  const renderContent = () => {
    const { checkPermission } = usePermissionCheck();
    const canAnswerInvite = checkPermission(Permission.ANSWER_INVITE);

    if (isFetching || answerInviteMutation.isPending) {
      return (
        <div className="flex items-center justify-center p-8">
          <Loading size="lg" />
        </div>
      );
    }

    if (!userInviteList || userInviteList.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center p-8 text-center :max-w-4xl">
          <Mail className="size-12 text-gray-400 mb-4" />
          <Text variant="body-lg" className="font-semibold mb-2">
            Nenhum convite encontrado
          </Text>
          <Text variant="body-md" className="text-secondary">
            Você não possui convites pendentes no momento.
          </Text>
        </div>
      );
    }

    return (
      <DataTable
        columns={inviteColumns}
        data={userInviteList || []}
        actions={canAnswerInvite ? inviteActions : []}
        keyField="invite_id"
      />
    );
  };

  return (
    <div className="max-h-96 overflow-y-auto">
      {renderContent()}
    </div>
  );
}

export const InviteListDialog = {
  Content: InviteListDialogContent
};