from seleniumbase import SB

telefones = [
'11934438135',
'31996264775',
'32999250748',
'38999547958',
'41997172130'
]

emails = [
'<EMAIL>',
'<EMAIL>',
'<EMAIL>'
]

cpfs = [
'06587643655',
'11045231673',
'11620199645',
'71108289649'
]

cnpjs = [
'05757597000218',
'16670085000155'
]

class ReportTypes:
    EMAIL = 'EMAIL'
    CPF = 'CPF'
    TELEFONE = 'TELEFONE'
    CNPJ = 'CNPJ'

def generate_reports(report_type, sb):
    cicle_through = []

    if report_type == ReportTypes.EMAIL:
        cicle_through = emails
    elif report_type == ReportTypes.CNPJ:
        cicle_through = cnpjs
    elif report_type == ReportTypes.CPF:
        cicle_through = cpfs
    elif report_type == ReportTypes.TELEFONE:
        cicle_through = telefones

    for el in cicle_through:
        sb.wait_for_element('button.btn-add')
        sb.click('button.btn-add')

        # Wait for and click the combobox to open dropdown
        sb.wait_for_element('button[role="combobox"]')
        sb.click('button[role="combobox"]')

        # Wait for dropdown options to appear and select one
        sb.wait_for_element(f'div[role="option"]:contains("{report_type}")')
        sb.click(f'div[role="option"]:contains("{report_type}")')

        sb.wait_for_element('input[placeholder*="Digite"]')
        sb.type('input[placeholder*="Digite"]', f"{el}")
        sb.click('button[data-testid="button-confirm-create-report"]')


def generate_pdfs(sb):

    hover_element_selector = "#root > main > div.flex-1.w-full.flex.flex-col.min-h-0 > section > div > div:nth-child(2) > div:nth-child(1) > div"
    icon_to_click_selector = "#root > main > div.flex-1.w-full.flex.flex-col.min-h-0 > section > div > div:nth-child(2) > div:nth-child(1) > div > div > div.icon.size-12.px-2"
    export_pdf_selector = "//*[contains(text(), 'exportar pdf')]"

    sb.hover_and_click(hover_element_selector, icon_to_click_selector)

    # 3. Click the "exportar pdf" option.
    #    The click() method also waits for the element to be clickable.
    print("Clicking the 'Exportar PDF' option...")
    for i in range(100):
        try:
            print(f"Attempt {i+1}: Trying to click the export PDF option...")
            sb.click(export_pdf_selector)

        except Exception as e:
            print(f"Attempt {i+1}: Element not found, retrying...")


with SB(uc=True, test=True, chromium_arg="--ignore-certificate-errors,--ignore-ssl-errors,--allow-running-insecure-content", timeout_multiplier=3) as sb:
    url = "https://localhost"
    sb.uc_open_with_reconnect(url)


    sb.click('button[data-testid="button-login-google"] div')
    # self.open("https://***************/")

    sb.click('button:contains("Visit Site")')
    sb.wait_for_element("#identifierId")
    sb.type("#identifierId", "<EMAIL>")
    sb.press_keys("#identifierId", "\n")  # Press Enter on the email field

    sb.wait_for_element('input[name="Passwd"]')
    sb.type('input[name="Passwd"]', "Senha@123")
    sb.press_keys('input[name="Passwd"]', "\n")  # Press Enter to submit

    sb.press_keys("input#user-password", "Senha@123\n")

    generate_pdfs(sb)

    # generate_reports(ReportTypes.EMAIL, sb)
    # generate_reports(ReportTypes.CNPJ, sb)
    # generate_reports(ReportTypes.CPF, sb)
    # generate_reports(ReportTypes.TELEFONE, sb)


    breakpoint()




# from playwright.sync_api import sync_playwright
# from playwright_stealth import stealth_sync
# #
# with sync_playwright() as p:
#     browser = p.chromium.launch(
#             headless=False,
#         )
#     page = browser.new_page()
#     stealth_sync(page)
#     page.goto("https://bot.sannysoft.com/")
#     page.screenshot(path=f"example_with_stealth.png", full_page=True)
#     browser.close()
#
# # from playwright.sync_api import sync_playwright
# # import threading
# #
# # def run_test():
# #     with sync_playwright() as p:
# #         browser = p.chromium.launch(headless=False)
# #         page = browser.new_page()
# #
# #         page.goto("http://***************")
# #
# #         # Example: fill a form
# #         page.fill("#username", "testuser")
# #         page.fill("#password", "123456")
# #         page.click("#login-button")
# #
# #         # Maybe wait for navigation
# #         page.wait_for_timeout(1000)
# #
# #         # Simulate more clicks
# #         page.click("#load-more")
# #         page.click("#submit-button")
# #
# #         browser.close()
# #
# # # Run N threads to simulate load
# # threads = []
# # for _ in range(10):  # adjust to desired concurrency
# #     t = threading.Thread(target=run_test)
# #     t.start()
# #     threads.append(t)
# #
# # for t in threads:
# #     t.join()
