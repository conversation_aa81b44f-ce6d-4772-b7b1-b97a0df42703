import { Kafka, EachMessagePayload } from "kafkajs";
import { GenerateReportsHtml } from "../generateReportsHtml";
import { mergePdfs } from "../../utils/pdfMerger";
import { Buffer } from "buffer";
import * as logger from '../../utils/logger';
import { loadTempPdfData, deleteTempPdfData, storeGeneratedPdf } from '../../config/minio';
import { IGenerateCoverPDFInput, IGenerateReportsHTMLOutput, IGenerateReportsPDFInput, PDFGeneratorFunction, CoverGeneratorFunction } from "../../global";

const PDF_REQUEST_TOPIC = "pdf-generation-requests";
const PDF_REPLY_TOPIC = "pdf-generation-results";
const PDF_LOG_TOPIC = "pdf-generation-logs";
const GROUP_ID = "pdf-service";

const kafka = new Kafka({
  brokers: [`${process.env.KAFKA_CONTAINER_NAME}:${process.env.KAFKA_INTERNAL_PORT}`,
  `${process.env.KAFKA_EXTERNAL_URI}:${process.env.KAFKA_EXTERNAL_PORT}`]
});
const consumer = kafka.consumer({
  groupId: GROUP_ID
});
const producer = kafka.producer();
const logProducer = kafka.producer();

async function logEvent(event: any) {
  await logProducer.send({
    topic: PDF_LOG_TOPIC,
    messages: [{ value: JSON.stringify(event) }]
  });
}

export async function startKafkaConsumer({ reportsPDFGenerator, pdfCoverGenerator }: {
  reportsPDFGenerator: PDFGeneratorFunction;
  pdfCoverGenerator: CoverGeneratorFunction;
}) {
  logger.info('Starting Kafka PDF Consumer...');
  await consumer.connect();
  await producer.connect();
  await logProducer.connect();
  logger.info('Connected to Kafka successfully');

  await consumer.subscribe({ topic: PDF_REQUEST_TOPIC });
  logger.info('Subscribed to pdf-generation-requests topic');

  logger.info('Kafka PDF Consumer is ready and waiting for messages...');

  await consumer.run({
    eachMessage: async ({ message }: EachMessagePayload) => {
      const kafkaMessage = JSON.parse(message.value!.toString());
      logger.info('Processing PDF generation request', { requestId: kafkaMessage.requestId });
      logEvent({ requestId: kafkaMessage.requestId, status: 'started', timestamp: Date.now(), details: { dataReference: kafkaMessage.dataReference } });

      try {
        // Validate Kafka message structure
        if (!kafkaMessage.requestId) {
          throw new Error('Missing required field: requestId');
        }
        if (!kafkaMessage.dataReference) {
          throw new Error('Missing required field: dataReference');
        }

        // Load actual PDF data from MinIO
        logger.info('Loading PDF data from MinIO', {
          requestId: kafkaMessage.requestId,
          dataReference: kafkaMessage.dataReference
        });

        const data = await loadTempPdfData(kafkaMessage.dataReference);
        if (!data) {
          throw new Error(`Failed to load PDF data from MinIO. Reference: ${kafkaMessage.dataReference}`);
        }

        // Delete temp data immediately after successful load
        logger.info('Deleting temp data after successful load', {
          requestId: kafkaMessage.requestId,
          dataReference: kafkaMessage.dataReference
        });
        await deleteTempPdfData(kafkaMessage.dataReference);

        // Validate loaded data structure
        if (!data.sections || !Array.isArray(data.sections)) {
          throw new Error('Missing required field: sections (must be array)');
        }
        if (!data.metadata) {
          throw new Error('Missing required field: metadata');
        }

        // Extract the same structure as HTTP controller
        const { sections, metadata, profile_image, should_print_snap_logo, organization_logo } = data;

        logger.info('Generating HTML for PDF request', {
          requestId: kafkaMessage.requestId,
          sectionsCount: sections?.length,
          reportName: metadata?.report_name
        });

        logger.info('Generating cover PDF', { requestId: kafkaMessage.requestId });
        const coverPdfBuffer = await pdfCoverGenerator({
          metadata,
          should_print_snap_logo,
          organization_logo
        });

        logger.info('Generating main report PDF', { requestId: kafkaMessage.requestId });
        const html = await GenerateReportsHtml({ sections, metadata, profile_image, should_print_snap_logo, organization_logo });
        const reportPdfBuffer = await reportsPDFGenerator({ ...html });

        logger.info('Merging cover and report PDFs', { requestId: kafkaMessage.requestId });
        const finalPdfBytes = await mergePdfs(coverPdfBuffer, reportPdfBuffer);
        const pdfBuffer = Buffer.from(finalPdfBytes);

        // Generate filename
        const filename = `${metadata.report_name || 'report'}.pdf`.replace(/[^a-zA-Z0-9.-]/g, '_');

        // Store PDF in MinIO instead of sending through Kafka
        const pdfReference = `pdf-${kafkaMessage.requestId}`;
        logger.info('Storing PDF in MinIO', {
          requestId: kafkaMessage.requestId,
          pdfReference: pdfReference,
          pdfSizeMB: (pdfBuffer.length / 1024 / 1024).toFixed(2),
          filename: filename
        });

        await storeGeneratedPdf(pdfReference, Buffer.from(pdfBuffer), filename);

        // Send only reference through Kafka (much smaller message)
        const resultMessage = {
          requestId: kafkaMessage.requestId,
          filename: filename,
          pdfReference: pdfReference,
          pdfSizeBytes: pdfBuffer.length,
          status: 'success',
          metadata: {
            report_type: kafkaMessage.metadata?.report_type,
            user_reports_id: kafkaMessage.metadata?.user_reports_id,
            user_id: kafkaMessage.metadata?.user_id
          }
        };

        logger.info('Result message', resultMessage);

        const messageSize = JSON.stringify(resultMessage).length;
        logger.info('Sending PDF reference to Kafka', {
          requestId: kafkaMessage.requestId,
          messageSize: messageSize,
          pdfReference: pdfReference,
          filename: filename
        });
        await producer.send({
          topic: PDF_REPLY_TOPIC,
          messages: [{
            key: kafkaMessage.requestId,
            value: JSON.stringify(resultMessage)
          }]
        });
        logger.info('PDF reference sent successfully', { requestId: kafkaMessage.requestId });



        logger.info('PDF generation completed successfully', { requestId: kafkaMessage.requestId });
        logEvent({ requestId: kafkaMessage.requestId, status: 'success', timestamp: Date.now(), filename: filename });
      } catch (err: any) {
        logger.error('PDF generation error', {
          requestId: kafkaMessage.requestId,
          error: err.message,
          stack: err.stack
        });
        logEvent({ requestId: kafkaMessage.requestId, status: 'error', timestamp: Date.now(), error: err.message });

        // Send error response back
        await producer.send({
          topic: PDF_REPLY_TOPIC,
          messages: [{
            key: kafkaMessage.requestId,
            value: JSON.stringify({
              requestId: kafkaMessage.requestId,
              error: err.message,
              status: 'error'
            })
          }]
        });
      }
    }
  });
}

process.on('SIGINT', async () => {
  logger.info('Received SIGINT, shutting down Kafka consumer gracefully...');

  await consumer.disconnect();
  await producer.disconnect();
  await logProducer.disconnect();
  logger.info('Kafka connections closed successfully');
  process.exit(0);
});

process.on('SIGTERM', async () => {
  logger.info('Received SIGTERM, shutting down Kafka consumer gracefully...');

  await consumer.disconnect();
  await producer.disconnect();
  await logProducer.disconnect();
  logger.info('Kafka connections closed successfully');
  process.exit(0);
});
