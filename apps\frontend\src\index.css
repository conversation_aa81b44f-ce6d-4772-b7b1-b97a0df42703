@import "@snap/design-system/dist/report.css";
@import "tailwindcss";
@plugin "tailwindcss-animate";


@custom-variant dark (&:is(.dark *));

:root {

  --accordion-header: #72758A;
  --accordion-content: #FFFFFF;
  --accordion-badge: #8A969D;

  --border-alt: #FFFFFF;

  --brand-coral: #fe473c;
  --neutral-400: oklch(0.74 0.0196 277.16);


  /* Table token colors */
  --table-header: #4E5F68CC;
  --table-header-alt: #37464DCC;
  --table-cell: transparent;
  --table-cell-alt: #37464D33;
  --table-border: oklch(0.85 0 0);
  --table-hover: oklch(0.9 0 0);

  /* modal tokens */
  --modal-header: oklch(0.95 0 0);
  --modal-header-alt: oklch(0.922 0 0);
  --modal-body: oklch(0.98 0 0);

  /* radio button token colors */
  --radio-button: #484a59;
  --radio-button-checked: #fe473c;

  /* overwrite tailwind light mode*/
  --color-neutral-100: #72758A;
  --color-neutral-400: #333540;
  --color-neutral-500: oklch(0.90 0 0);
  --color-neutral-600: #18191F;
  --color-neutral-700: oklch(0.85 0 0);
  --color-neutral-800: #0C0C10;

  --color-border: #37464D33;

  --reports-secondary: #889EA3;
}

.dark {

  --accordion-header: #2E2F3A;
  --accordion-content: #15151B;
  --accordion-badge: #1E1F27;

  --border-alt: #292A33;

  --brand-coral: #fe473c;

  /* Table token colors */
  --table-header: #4E5F68CC;
  --table-header-alt: #37464DCC;
  --table-cell: transparent;
  --table-cell-alt: #37464D33;
  --table-border: #5D6071;
  --table-hover: #5D60711A;

  /* Modal token colors */
  --modal-header: #fe473c;
  --modal-header-alt: #18191F;
  --modal-body: #1E1F27;

  /* radio button token colors */
  --radio-button: #1E1F27;
  --radio-button-checked: #fe473c;

  /* button token colors */
  --button: #1E1F27;
  --button-foreground: #FFFFFF;
  --button-hover: #292A33;
  --button-active: #24252E;

  /* override tailwind neutral palette dark mode */
  --color-neutral-100: #72758A;
  --color-neutral-400: #333540;
  --color-neutral-500: #1E1F27;
  --color-neutral-600: #18191F;
  --color-neutral-700: #121317;
  --color-neutral-800: #0C0C10;

  --color-border: #5D6071;
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--reports-secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --color-accordion-header: var(--accordion-header);
  --color-accordion-content: var(--accordion-content);
  --color-accordion-badge: var(--accordion-badge);
  --color-brand-primary: var(--brand-coral);
  --color-dialog: var(--neutral-400);

  /* Table token colors */
  --color-table-header: var(--table-header);
  --color-table-header-alt: var(--table-header-alt);
  --color-table-cell: var(--table-cell);
  --color-table-cell-alt: var(--table-cell-alt);
  --color-table-border: var(--table-border);
  --color-table-hover: var(--table-hover);

  /* Modal token colors */
  --color-modal-header: var(--modal-header);
  --color-modal-header-alt: var(--modal-header-alt);
  --color-modal-body: var(--modal-body);

  /* Radio button token colors */
  --color-radio-button: var(--radio-button);
  --color-radio-button-checked: var(--radio-button-checked);

  /* overwrite tailwind */
  --color-border: var(--color-border);
  --color-border-alt: var(--border-alt);
}

body {
  background-image: radial-gradient(oklch(0.3 0 0) 1px, transparent 1px);
  background-size: 32px 32px;
  overflow: hidden;
}

::-webkit-scrollbar {
  width: 0.625rem;
  height: 0.625rem;
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

::-webkit-scrollbar-thumb {
  border-radius: 9999px;
  border: 1px solid transparent;
  background-clip: padding-box;
  background-color: oklch(0.3 0 0);
}