import os
import time
import requests
import docker
import logging

# Logging config
logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s: %(message)s")
logger = logging.getLogger(__name__)

# Environment variables
PROMETHEUS_URL = os.getenv("PROMETHEUS_URL", "http://prometheus:9090")
PROM_QUERY = os.getenv(
    "PROM_QUERY",
    'sum(kafka_consumergroup_lag{consumergroup="%s", topic="%s"})' % (
        os.getenv("GROUP_ID", "pdf-service"),
        os.getenv("TOPIC", "pdf-topic")
    )
)
SERVICE_NAME = os.getenv("SERVICE_NAME", "mystack_pdf")

POLL_INTERVAL = int(os.getenv("POLL_INTERVAL_MS", "5000")) / 1000  # seconds
HIGH_LAG_THRESHOLD = int(os.getenv("HIGH_LAG_THRESHOLD", "1000"))
LOW_LAG_THRESHOLD = int(os.getenv("LOW_LAG_THRESHOLD", "200"))
DOWNSCALE_STABLE_COUNT = int(os.getenv("DOWNSCALE_STABLE_COUNT", "3"))

MAX_REPLICAS = int(os.getenv("MAX_REPLICAS", "8"))
MIN_REPLICAS = int(os.getenv("MIN_REPLICAS", "1"))
SCALE_UP_REPLICAS = int(os.getenv("SCALE_UP_REPLICAS", "4"))

COOLDOWN_SECONDS = int(os.getenv("COOLDOWN_SECONDS", "10"))

# State
stable_low_count = 0
last_action_time = 0

# Docker client
docker_client = docker.from_env()


def get_total_lag():
    """Query Prometheus for Kafka lag."""
    try:
        logger.info(f"Querying Prometheus at {PROMETHEUS_URL} with query: {PROM_QUERY}")
        resp = requests.get(f"{PROMETHEUS_URL}/api/v1/query", params={"query": PROM_QUERY}, timeout=5)
        resp.raise_for_status()
        data = resp.json()
        logger.info(f"Prometheus response status: {data.get('status')}")
        if data["status"] != "success":
            logger.warning("Prometheus query failed: %s", data)
            return 0
        result = data["data"]["result"]
        if not result:
            logger.info("No results from Prometheus query")
            return 0
        lag_value = int(float(result[0]["value"][1]))
        logger.info(f"Retrieved lag value: {lag_value}")
        return lag_value
    except Exception as e:
        logger.error("Error querying Prometheus: %s", e)
        return 0


def get_current_replicas():
    """Get current replica count of the service."""
    try:
        logger.info(f"Getting current replicas for service: {SERVICE_NAME}")
        service = docker_client.services.get(SERVICE_NAME)
        spec = service.attrs["Spec"]
        replicas = spec["Mode"]["Replicated"]["Replicas"]
        logger.info(f"Current replicas for {SERVICE_NAME}: {replicas}")
        return replicas
    except Exception as e:
        logger.error(f"Failed to get current replicas for {SERVICE_NAME}: {e}")
        raise


def set_replicas(count):
    """Scale the service to the given replica count."""
    count = max(MIN_REPLICAS, min(MAX_REPLICAS, count))
    logger.info(f"Attempting to scale {SERVICE_NAME} to {count} replicas")
    service = docker_client.services.get(SERVICE_NAME)
    service.update(mode=docker.types.ServiceMode('replicated', replicas=count))
    logger.info(f"Successfully scaled {SERVICE_NAME} to {count} replicas")


def main():
    global stable_low_count, last_action_time

    logger.info(f"Starting autoscaler for {SERVICE_NAME}")
    logger.info(f"Prometheus URL: {PROMETHEUS_URL}")
    logger.info(f"Query: {PROM_QUERY}")
    logger.info(f"High lag threshold: {HIGH_LAG_THRESHOLD}")
    logger.info(f"Low lag threshold: {LOW_LAG_THRESHOLD}")
    logger.info(f"Min replicas: {MIN_REPLICAS}, Max replicas: {MAX_REPLICAS}")

    while True:
        total_lag = get_total_lag()
        logger.info(f"Total lag: {total_lag}")

        now = time.time()
        if now - last_action_time < COOLDOWN_SECONDS:
            logger.debug(f"In cooldown period, skipping scaling decision")
            time.sleep(POLL_INTERVAL)
            continue

        try:
            current_replicas = get_current_replicas()
        except Exception as e:
            logger.error(f"Failed to get current replicas: {e}")
            time.sleep(POLL_INTERVAL)
            continue

        # Scale up
        if total_lag > HIGH_LAG_THRESHOLD:
            logger.info(f"High lag detected ({total_lag} > {HIGH_LAG_THRESHOLD}), scaling up")
            desired = max(current_replicas + 1, SCALE_UP_REPLICAS)
            if desired > current_replicas:
                set_replicas(desired)
                last_action_time = now
                stable_low_count = 0
            time.sleep(POLL_INTERVAL)
            continue

        # Hysteresis zone (do nothing)
        if LOW_LAG_THRESHOLD <= total_lag <= HIGH_LAG_THRESHOLD:
            logger.debug(f"In hysteresis zone ({LOW_LAG_THRESHOLD} <= {total_lag} <= {HIGH_LAG_THRESHOLD}), no action")
            stable_low_count = 0
            time.sleep(POLL_INTERVAL)
            continue

        # Scale down
        if total_lag < LOW_LAG_THRESHOLD:
            stable_low_count += 1
            logger.info(f"Low lag detected ({total_lag} < {LOW_LAG_THRESHOLD}), stable count: {stable_low_count}/{DOWNSCALE_STABLE_COUNT}")
            if stable_low_count >= DOWNSCALE_STABLE_COUNT and current_replicas > MIN_REPLICAS:
                logger.info(f"Scaling down to minimum replicas ({MIN_REPLICAS})")
                set_replicas(MIN_REPLICAS)
                last_action_time = now
                stable_low_count = 0

        time.sleep(POLL_INTERVAL)


if __name__ == "__main__":
    main()
