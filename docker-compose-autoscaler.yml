services:
  kafka-autoscaler:
    image: my-autoscaler-image
    environment:
      PROMETHEUS_URL: "http://prometheus:9090"
      TOPIC: "pdf-generation-requests"
      GROUP_ID: "pdf-service"
      SERVICE_NAME: "mystack_pdf"
      POLL_INTERVAL_MS: "5000"
      HIGH_LAG_THRESHOLD: "5"
      LOW_LAG_THRESHOLD: "2"
      DOWNSCALE_STABLE_COUNT: "3"
      MIN_REPLICAS: "1"
      MAX_REPLICAS: "8"
      SCALE_UP_REPLICAS: "4"
      COOLDOWN_SECONDS: "10"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
    networks:
      - mystack-net
      - monitoring
