import { Text, Button } from "@snap/design-system";
import { Building2, Check } from "lucide-react";
import { AiOutlineLoading3Quarters } from "react-icons/ai";

interface OrganizationChangeConfirmationProps {
  currentOrganization: string;
  newOrganization: string;
  onConfirm?: () => void;
  onCancel?: () => void;
}

interface OrganizationChangeConfirmationFooterProps {
  onConfirm?: () => void;
  onCancel?: () => void;
  isLoading?: boolean;
}

function OrganizationChangeConfirmation({
  currentOrganization,
  newOrganization,
}: OrganizationChangeConfirmationProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-3 p-4 bg-accent/10 border border-accent rounded-sm">
        <Building2 className="size-5 text-white flex-shrink-0" />
        <div>
          <Text variant="body-md" className="font-semibold">
            Atenção: Você está prestes a mudar de organização.
          </Text>
          <Text variant="body-sm" className="mt-1">
            <PERSON><PERSON> ace<PERSON>, você sairá da organização atual e ingressará na nova.
          </Text>
          <Text variant="body-sm" className="mt-1">
            Od dados gerados na organização atual permanecerão salvos, mas ficarão inacessíveis.
          </Text>
          <Text variant="body-sm" className="mt-1">
            Em seguida, sua sessão atual terminará e você será redirecionado à tela de login.
          </Text>
        </div>
      </div>

      <div className="space-y-3">
        <div>
          <Text variant="body-sm" className="font-medium text-secondary pb-1">
            Organização atual:
          </Text>
          <Text variant="body-md">
            {currentOrganization}
          </Text>
        </div>

        <div>
          <Text variant="body-sm" className="font-medium text-secondary pb-1">
            Nova organização:
          </Text>
          <Text variant="body-md">
            {newOrganization}
          </Text>
        </div>
      </div>

      <Text >
        Tem certeza de que deseja continuar?
      </Text>
    </div>
  );
}

function OrganizationChangeConfirmationFooter({ onConfirm, onCancel, isLoading }: OrganizationChangeConfirmationFooterProps) {
  return (
    <div className="flex gap-3 justify-end">
      <Button
        onClick={onCancel}
        className="min-w-[100px] uppercase !bg-transparent"
      >
        Voltar
      </Button>
      <Button
        className="min-w-[100px] uppercase !bg-foreground !text-background !font-bold disabled:opacity-50 hover:opacity-80 transition-opacity"
        onClick={onConfirm}
        icon={isLoading ? <AiOutlineLoading3Quarters size={16} className="animate-spin" /> : <Check size={16} />}
        iconPosition="right"
        disabled={isLoading}
      >
        Confirmar Mudança
      </Button>
    </div>
  );
}

export const OrganizationChangeDialog = {
  Content: OrganizationChangeConfirmation,
  Footer: OrganizationChangeConfirmationFooter
};