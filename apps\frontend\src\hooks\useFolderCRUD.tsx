import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { useParams } from "react-router";
import { toast } from "sonner";
import { useCreateFolderActions } from "~/store/createFolderStore";
import { useDialogActions } from "~/store/dialogStore";
import { createErrorHandler } from "~/helpers/errorHandling.helper";
import { useEncryption } from "~/hooks/useEncryption";
import { EncryptedPayload, FolderPayload, MoveFilesPayload } from "~/types/global";
import { useReportCRUD } from "~/hooks/useReportCRUD";
import {
  deleteFolder,
  renameFolder,
  createFolder,
  moveFilesBetweenFolders,
  getFolders,
  getFoldersExceptContains,
  moveFolderToFolder,
  FolderApiResponse
} from "~/services/gateways/folder.gateway";
import { REPORT_CONSTANTS } from "~/helpers/constants";
import { FolderListItem, useFolderListFilters } from "~/store/folderListStore";
import { decryptListItem } from "~/helpers";

export interface CreateFolderRequest {
  folderName: string;
  selectedReports?: string[];
  parentFolderId?: string | null;
}

export interface CreateFolderResponse {
  folder_id: string;
  folder_name: string;
  created_at: string;
  reports_count: number;
}

export const useFolderCRUD = () => {
  const { folderId } = useParams<{ folderId?: string }>();
  const { invalidateCurrentFolderNoFilters } = useReportCRUD(folderId || null);
  const queryClient = useQueryClient();
  const { closeDialog } = useDialogActions();
  const { encryptData, encryptNgrams } = useEncryption();
  const {
    clearFolderData
  } = useCreateFolderActions();

  const createFolderMutation = useMutation({
    mutationFn: async (payload: FolderPayload) => {
      try {
        const folderName = payload.folderName
        console.log("[useFolderCRUD] createFolderMutation folderName", folderName);
        const encryptedFolderName = await encryptData(folderName);
        if (!encryptedFolderName.success || !encryptedFolderName.data) {
          throw new Error("Erro ao criptografar nome da pasta");
        }
        // HMAC
        const hmacResult = await encryptNgrams({
          folder_name: folderName,
        });
        const apiPayload: FolderPayload = {
          [REPORT_CONSTANTS.new_folder.folder_name]: encryptedFolderName.data as EncryptedPayload,
          [REPORT_CONSTANTS.new_folder.parent_folder_id]: folderId || null,
          [REPORT_CONSTANTS.new_folder.user_reports_id_list]: payload.selectedReports || [],
          [REPORT_CONSTANTS.new_folder.hmac_folder_name]: hmacResult.folder_name || [],
        };

        console.log("[useFolderCRUD] createFolderMutation payload", apiPayload);
        return createFolder(apiPayload);
      } catch (error) {
        console.error("Error creating folder:", error);
        throw error;
      }
    },
    onSuccess: async (data: any) => {
      invalidateCurrentFolderNoFilters(folderId || null);
      clearFolderData();
      closeDialog();

      toast.success(`Pasta criada com sucesso!`, {
        description: `Pasta criada com ${data?.reports_count || 0} relatórios`,
      });
    },
    onError: (error: Error) => {
      createErrorHandler(
        "Ocorreu um erro ao tentar criar a pasta",
        "Erro ao criar pasta"
      )(error);
    },
  });

  const deleteFolderMutation = useMutation({
    mutationFn: async (folderId: string) => {
      try {
        await deleteFolder(folderId);
      } catch (error) {
        console.error("Error deleting folder:", error);
        throw error;
      }
    },
    onSuccess: async (data: any) => {
      invalidateCurrentFolderNoFilters(folderId || null);
      closeDialog();

      toast.success(`Pasta deletada com sucesso!`);
    },
    onError: (error: Error) => {
      createErrorHandler(
        "Ocorreu um erro ao tentar deletar a pasta",
        "Erro ao deletar pasta"
      )(error);
    },
  });

  const renameFolderMutation = useMutation({
    mutationFn: async (payload: FolderPayload) => {
      try {
        // HMAC
        const hmacResult = await encryptNgrams({
          folder_name: payload.folder_name,
        });
        // CRIPTOGRAFIA
        const encryptedFolderName = await encryptData(payload.folder_name);
        await renameFolder({
          [REPORT_CONSTANTS.new_folder.folder_id]: payload.folder_id,
          [REPORT_CONSTANTS.new_folder.folder_name]: encryptedFolderName.data as EncryptedPayload,
          [REPORT_CONSTANTS.new_folder.hmac_folder_name]: hmacResult.folder_name || [],
        });
      } catch (error) {
        console.error("Error renaming folder:", error);
        throw error;
      }
    },
    onSuccess: async (data: any) => {
      invalidateCurrentFolderNoFilters(folderId || null);
      closeDialog();

      toast.success(`Pasta renomeada com sucesso!`);
    },
    onError: (error: Error) => {
      createErrorHandler(
        "Ocorreu um erro ao tentar renomear a pasta",
        "Erro ao renomear pasta"
      )(error);
    },
  });

  const moveFilesBetweenFoldersMutation = useMutation({
    mutationFn: async (payload: MoveFilesPayload) => {
      try {
        await moveFilesBetweenFolders(payload);
      } catch (error) {
        console.error("Error moving files between folders:", error);
        throw error;
      }
    },
    onSuccess: async (data: any) => {
      invalidateCurrentFolderNoFilters(folderId || null);
      closeDialog();

      toast.success(`Arquivos movidos com sucesso!`);
    },
    onError: (error: Error) => {
      createErrorHandler(
        "Ocorreu um erro ao tentar mover os arquivos",
        "Erro ao mover arquivos"
      )(error);
    },
  });

  const moveReportToFolderMutation = useMutation({
    mutationFn: async (payload: {
      reportId: string;
      sourceFolderId?: string | null;
      destinationFolderId?: string | null;
    }) => {
      try {
        const movePayload: MoveFilesPayload = {
          [REPORT_CONSTANTS.move_files.src_folder_id]: payload.sourceFolderId || null,
          [REPORT_CONSTANTS.move_files.dest_folder_id]: payload.destinationFolderId || null,
          [REPORT_CONSTANTS.move_files.user_reports_id_list]: [payload.reportId],
          [REPORT_CONSTANTS.move_files.folder_id_list]: null,
        };

        console.log("[useFolderCRUD] moveReportToFolderMutation payload", movePayload);
        await moveFilesBetweenFolders(movePayload);
      } catch (error) {
        console.error("Error moving report to folder:", error);
        throw error;
      }
    },
    onSuccess: async (data: any, variables) => {
      invalidateCurrentFolderNoFilters(variables.sourceFolderId || null);
      if (variables.destinationFolderId !== variables.sourceFolderId) {
        invalidateCurrentFolderNoFilters(variables.destinationFolderId || null);
      }
      closeDialog();

      toast.success(`Relatório movido com sucesso!`);
    },
    onError: (error: Error) => {
      createErrorHandler(
        "Ocorreu um erro ao tentar mover o relatório",
        "Erro ao mover relatório"
      )(error);
    },
  });

  const moveFolderToFolderMutation = useMutation({
    mutationFn: async (payload: {
      folderId: string;
      sourceFolderId?: string | null;
      destinationFolderId?: string | null;
    }) => {
      try {
        const movePayload: MoveFilesPayload = {
          [REPORT_CONSTANTS.move_files.src_folder_id]: payload.sourceFolderId || null,
          [REPORT_CONSTANTS.move_files.dest_folder_id]: payload.destinationFolderId || null,
          [REPORT_CONSTANTS.move_files.user_reports_id_list]: null,
          [REPORT_CONSTANTS.move_files.folder_id_list]: [payload.folderId],
        };

        console.log("[useFolderCRUD] moveFolderToFolderMutation payload", movePayload);
        await moveFolderToFolder(movePayload);
      } catch (error) {
        console.error("Error moving folder:", error);
        throw error;
      }
    },
    onSuccess: async (data: any, variables) => {
      invalidateCurrentFolderNoFilters(variables.sourceFolderId || null);
      if (variables.destinationFolderId !== variables.sourceFolderId) {
        invalidateCurrentFolderNoFilters(variables.destinationFolderId || null);
      }
      closeDialog();

      toast.success(`Pasta movida com sucesso!`);
    },
    onError: (error: Error) => {
      createErrorHandler(
        "Ocorreu um erro ao tentar mover a pasta",
        "Erro ao mover pasta"
      )(error);
    },
  });

  return {
    createFolderMutation,
    deleteFolderMutation,
    renameFolderMutation,
    moveFilesBetweenFoldersMutation,
    moveReportToFolderMutation,
    moveFolderToFolderMutation,
  };
};

export interface FolderListQueryParams {
  folder_id?: string;
  hmac_folder_name?: string;
  excludeFolderId?: string;
}

export const useFolderListQuery = (params?: FolderListQueryParams | string) => {
  const { decryptData } = useEncryption();
  const queryParams: FolderListQueryParams = typeof params === 'string'
    ? { excludeFolderId: params }
    : params || {};

  return useQuery({
    queryKey: ['folders', queryParams.excludeFolderId],
    queryFn: async (): Promise<FolderListItem[]> => {
      try {
        const payload: { folder_id?: string; hmac_folder_name?: string } = {};

        // Pass excludeFolderId as folder_id to the API
        if (queryParams.excludeFolderId) {
          payload.folder_id = queryParams.excludeFolderId;
        }

        if (queryParams.folder_id) {
          payload.folder_id = queryParams.folder_id;
        }

        if (queryParams.hmac_folder_name) {
          payload.hmac_folder_name = queryParams.hmac_folder_name;
        }

        const response = await getFolders(payload) as FolderApiResponse | FolderListItem[];

        // Handle the new response structure with pagination
        const folderData = Array.isArray(response) ? response : response.data;

        const decryptedFolders = await Promise.all(
          folderData.map(async (item, index) => {
            try {
              return await decryptListItem(item, decryptData);
            } catch (error) {
              console.error(`Error decrypting item #${index + 1}:`, error);
              throw error;
            }
          })
        );

        return decryptedFolders as FolderListItem[];
      } catch (error) {
        console.error("Error fetching folder list:", error);
        throw error;
      }
    },
    retry: 2,
    staleTime: 0,
  });
};

export const useFolderListExceptContainsSimpleQuery = (params?: FolderListQueryParams | string) => {
  const { decryptData } = useEncryption();
  const queryParams: FolderListQueryParams = typeof params === 'string'
    ? { excludeFolderId: params }
    : params || {};

  return useQuery({
    queryKey: ['foldersExceptContainsSimple', queryParams.excludeFolderId],
    queryFn: async (): Promise<FolderListItem[]> => {
      try {
        const payload: { folder_id?: string; hmac_folder_name?: string } = {};

        if (queryParams.excludeFolderId) {
          payload.folder_id = queryParams.excludeFolderId;
        }

        if (queryParams.folder_id) {
          payload.folder_id = queryParams.folder_id;
        }

        if (queryParams.hmac_folder_name) {
          payload.hmac_folder_name = queryParams.hmac_folder_name;
        }

        const response = await getFoldersExceptContains(payload) as FolderApiResponse | FolderListItem[];

        const folderData = Array.isArray(response) ? response : response.data;

        const decryptedFolders = await Promise.all(
          folderData.map(async (item, index) => {
            try {
              return await decryptListItem(item, decryptData);
            } catch (error) {
              console.error(`Error decrypting item #${index + 1}:`, error);
              throw error;
            }
          })
        );

        return decryptedFolders as FolderListItem[];
      } catch (error) {
        console.error("Error fetching folder list except contains:", error);
        throw error;
      }
    },
    retry: 2,
    staleTime: 0,
  });
};

export interface FolderListWithPaginationResult {
  folders: FolderListItem[];
  pagination: {
    current_page: number;
    total_pages: number;
    total_items: number;
    page_size: number;
    has_next: boolean;
    has_previous: boolean;
  };
}

export const useFolderListWithPaginationQuery = (params?: FolderListQueryParams | string) => {
  const { decryptData } = useEncryption();
  const filters = useFolderListFilters();
  const queryParams: FolderListQueryParams = typeof params === 'string'
    ? { excludeFolderId: params }
    : params || {};

  return useQuery({
    queryKey: ['foldersWithPagination', queryParams.excludeFolderId, filters.page, filters.limit],
    queryFn: async (): Promise<FolderListWithPaginationResult> => {
      try {
        const payload: {
          folder_id?: string;
          hmac_folder_name?: string;
          column_order: string;
          order: string;
          limit: number;
          page: number;
        } = {
          column_order: filters.column_order,
          order: filters.order,
          limit: filters.limit,
          page: filters.page,
        };

        if (queryParams.excludeFolderId) {
          payload.folder_id = queryParams.excludeFolderId;
        }

        if (queryParams.folder_id) {
          payload.folder_id = queryParams.folder_id;
        }

        if (queryParams.hmac_folder_name) {
          payload.hmac_folder_name = queryParams.hmac_folder_name;
        }

        const response = await getFolders(payload);

        const decryptedFolders = await Promise.all(
          response.data.map(async (item, index) => {
            try {
              return await decryptListItem(item, decryptData);
            } catch (error) {
              console.error(`Error decrypting item #${index + 1}:`, error);
              throw error;
            }
          })
        );

        return {
          folders: decryptedFolders as FolderListItem[],
          pagination: response.pagination,
        };
      } catch (error) {
        console.error("Error fetching folder list with pagination:", error);
        throw error;
      }
    },
    retry: 2,
    staleTime: 0,
  });
};

export const useFolderListExceptContainsQuery = (params?: FolderListQueryParams | string) => {
  const { decryptData } = useEncryption();
  const filters = useFolderListFilters();
  const queryParams: FolderListQueryParams = typeof params === 'string'
    ? { excludeFolderId: params }
    : params || {};

  return useQuery({
    queryKey: ['foldersExceptContains', queryParams.excludeFolderId, filters.page, filters.limit],
    queryFn: async (): Promise<FolderListWithPaginationResult> => {
      try {
        const payload: {
          folder_id?: string;
          hmac_folder_name?: string;
          column_order: string;
          order: string;
          limit: number;
          page: number;
        } = {
          column_order: filters.column_order,
          order: filters.order,
          limit: filters.limit,
          page: filters.page,
        };

        if (queryParams.excludeFolderId) {
          payload.folder_id = queryParams.excludeFolderId;
        }

        if (queryParams.folder_id) {
          payload.folder_id = queryParams.folder_id;
        }

        if (queryParams.hmac_folder_name) {
          payload.hmac_folder_name = queryParams.hmac_folder_name;
        }

        const response = await getFoldersExceptContains(payload);

        const decryptedFolders = await Promise.all(
          response.data.map(async (item, index) => {
            try {
              return await decryptListItem(item, decryptData);
            } catch (error) {
              console.error(`Error decrypting item #${index + 1}:`, error);
              throw error;
            }
          })
        );

        return {
          folders: decryptedFolders as FolderListItem[],
          pagination: response.pagination,
        };
      } catch (error) {
        console.error("Error fetching folder list except contains:", error);
        throw error;
      }
    },
    retry: 2,
    staleTime: 0,
  });
};
