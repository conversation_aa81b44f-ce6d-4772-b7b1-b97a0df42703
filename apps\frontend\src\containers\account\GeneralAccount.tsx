import { Button, Input, Separator, Text } from '@snap/design-system'
import { Info, Save } from 'lucide-react'
import { useEffect, useState } from 'react';
import { AiOutlineLoading3Quarters } from 'react-icons/ai';
import { toast } from 'sonner';
import { USER_CONSTANTS } from '~/helpers/constants';
import { useUserData } from '~/store/userStore'
import { useUserCRUD } from '~/hooks/useUserCRUD';
import { Checkbox } from "~/components/ui/checkbox";
import { usePermissionCheck } from '~/components/router/PermissionGuard';
import { Permission } from "~/helpers/permissions.helper";

const GeneralAccount = () => {
  const userData = useUserData();
  const { checkPermission } = usePermissionCheck();
  const { setApiKeyMutation, updateUseSnapLogoMutation } = useUserCRUD();
  const profile = userData?.[USER_CONSTANTS.user_data.account_type as keyof typeof userData]
  const organizationName = userData?.[USER_CONSTANTS.user_data.organization_name as keyof typeof userData]
  const admin = USER_CONSTANTS.profile_types.administrador
  const isAdmin = profile === admin
  const savedApiKey = userData?.[USER_CONSTANTS.user_data.api_key as keyof typeof userData]
  const [apiKey, setApiKey] = useState('');
  const [checked, setChecked] = useState(true);
  const canUpdatePrintSnapLogo = checkPermission(Permission.PRINT_SNAP_LOGO)

  useEffect(() => {
    if (savedApiKey) {
      setApiKey(savedApiKey as string);
    }
  }, [savedApiKey])

  useEffect(() => {
    if (isAdmin && userData?.print_snap_logo !== undefined) {
      setChecked(userData.print_snap_logo as boolean);
    }
  }, [userData?.print_snap_logo, isAdmin])

  const handleSave = () => {
    if (!apiKey) {
      toast.error('Erro ao cadastrar chave!', {
        description: 'A chave não pode estar vazia.'
      });
      return;
    }

    if (!apiKey.match(/^[a-zA-Z0-9]{32}$/)) {
      toast.error('A chave deve conter 32 caracteres alfanuméricos.');
      return;
    }

    setApiKeyMutation.mutate(apiKey);
  }

  const handleCheckboxChange = (checkedValue: boolean) => {
    // Optimistic update - immediately update the UI
    setChecked(checkedValue);

    // Call the API mutation
    updateUseSnapLogoMutation.mutate(checkedValue, {
      onError: () => {
        // Revert the optimistic update on error
        setChecked(!checkedValue);
      }
    });
  }

  return (
    <div className="flex flex-col gap-4 justify-center p-4">
      <div className='pl-1 flex flex-col gap-1'>
        <Text variant="body-lg" className="font-semibold">Insira a <span className='text-accent'>chave da API</span> que deseja cadastrar:</Text>
        <Text variant="body-sm" className="opacity-80">Exemplo: 25t8es490gb6a820855a40096g51d987</Text>
      </div>
      <div className='flex gap-4 border border-border rounded-md p-8'>
        <div className='flex items-center  flex-1 gap-4'>
          <Input
            type="text"
            placeholder='Insira aqui a chave da API'
            variant='filled'
            onChange={(e) => setApiKey(e.target.value)}
            value={apiKey}
            className='rounded-none border-0 py-1.5 bg-background'
            wrapperClassName='flex-1 max-w-lg border-1 border-foreground'
          />
          <Button
            variant="outline"
            onClick={handleSave}
            disabled={setApiKeyMutation.isPending}
            iconPosition="right"
            className='bg-transparent'
            icon={setApiKeyMutation.isPending ?
              <AiOutlineLoading3Quarters size={20} className="animate-spin" /> :
              <Save size={20} />
            }
          >
            Salvar
          </Button>
        </div>
        {
          isAdmin && (
            <div className='flex items-start gap-2 flex-1'>
              <Info size={16} className='text-accent' />
              <Text variant="body-md leading-normal">A chave será cadastrada para a <span className='font-semibold text-accent'>{`organização: ${organizationName}`} </span> </Text>
            </div>
          )
        }
      </div>
      {
        isAdmin && canUpdatePrintSnapLogo ? (
          <div className='py-4'>
            <Separator className='mb-8' />
            <div className='flex items-center gap-4'>
              {updateUseSnapLogoMutation.isPending ? (
                <AiOutlineLoading3Quarters size={16} className="animate-spin" />
              ) : (
                <Checkbox
                  id="use-logo"
                  checked={checked}
                  onCheckedChange={handleCheckboxChange}
                  className="border-foreground data-[state=checked]:bg-foreground rounded-none cursor-pointer"
                  disabled={updateUseSnapLogoMutation.isPending}
                />)
              }
              <Text variant="body-lg" className="font-semibold">Usar a logo <span className='text-accent'>SNAP</span> nos relatórios impressos da organização.</Text>
            </div>
          </div>
        ) : null
      }
    </div>
  )
}

export default GeneralAccount