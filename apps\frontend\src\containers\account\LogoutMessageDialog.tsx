import { Text, Button } from "@snap/design-system";
import { Check, LogOut } from "lucide-react";
import { AiOutlineLoading3Quarters } from "react-icons/ai";

interface LogoutMessageConfirmationProps {
  onConfirm?: () => void;
  onCancel?: () => void;
}

interface LogoutMessageConfirmationFooterProps {
  onConfirm?: () => void;
  onCancel?: () => void;
  isLoading?: boolean;
}

function LogoutMessageConfirmation({ }: LogoutMessageConfirmationProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-3 p-4 bg-accent/10 border border-accent rounded-sm">
        <LogOut className="size-5 text-white flex-shrink-0" />
        <div>
          <Text variant="body-md" className="font-semibold">
            Você confirma o aceite do convite?
          </Text>
          <Text className="mt-1">
            Ao clicar em SIM, sua sessão atual terminará e você será redirecionado à tela de login.
          </Text>
        </div>
      </div>
    </div>
  );
}

function LogoutMessageConfirmationFooter({ onConfirm, onCancel, isLoading }: LogoutMessageConfirmationFooterProps) {
  return (
    <div className="flex gap-3 justify-end">
      <Button
        onClick={onCancel}
        className="min-w-[100px] uppercase !bg-transparent">
        Voltar
      </Button>
      <Button
        onClick={onConfirm}
        className="min-w-[100px] uppercase !bg-foreground !text-background !font-bold disabled:opacity-50 hover:opacity-80 transition-opacity"
        icon={isLoading ? <AiOutlineLoading3Quarters size={16} className="animate-spin" /> : <Check size={16} />}
        iconPosition="right"
        disabled={isLoading}
      >
        Sim
      </Button>
    </div>
  );
}

export const LogoutMessageDialog = {
  Content: LogoutMessageConfirmation,
  Footer: LogoutMessageConfirmationFooter
};