import { Button, <PERSON> } from "@snap/design-system";
import { Check, Folder, X } from "lucide-react";
import { useMemo } from "react";
import { DataTable, Column } from "~/components/Table";
import { useFolderListExceptContainsQuery, useFolderCRUD } from "~/hooks/useFolderCRUD";
import { FolderListItem, useSelectedFolder, useFolderListActions, useFolderListFilters, useFolderListTotalItems } from "~/store/folderListStore";
import { useDialogActions } from "~/store/dialogStore";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import { FaFolder } from "react-icons/fa";
import { formatIsoDate } from "~/helpers";

interface MoveFolderDialogContentProps {
  currentFolderId?: string;
}

export function MoveFolderDialogContent({ currentFolderId }: MoveFolderDialogContentProps) {
  const { setSelectedFolder, setPage, setTotalItems } = useFolderListActions();
  const filters = useFolderListFilters();
  const totalItems = useFolderListTotalItems();
  const { data, isLoading, error, refetch } = useFolderListExceptContainsQuery(currentFolderId);

  const folderList = data?.folders || [];
  const pagination = data?.pagination;

  if (pagination && pagination.total_items !== totalItems) {
    setTotalItems(pagination.total_items);
  }

  const folderListWithRoot = useMemo(() => {
    const rootFolder: FolderListItem = {
      folder_id: "root",
      folder_name: "Início - Dashboard",
      parent_folder_id: null,
      folder_path: "/",
      depth_level: 0,
      created_at: "-",
      modified_at: "-"
    };
    return [rootFolder, ...folderList];
  }, [folderList]);

  const columns: Column<FolderListItem>[] = useMemo(() => [
    {
      key: "folder_name",
      header: "Selecione a pasta de destino",
      widthClass: "w-3/5 min-w-[250px]",
      className: "overflow-hidden",
      render: (_, row: FolderListItem) => (
        <div className="flex items-center gap-3">
          <div className="truncate">
            <Text variant="body-md" className="font-semibold">
              {row.folder_name}
            </Text>
          </div>
          <Folder className="size-4 flex-shrink-0" />
        </div>
      ),
    },
    {
      key: "modified_at",
      header: "Modificada em",
      widthClass: "w-1/5 min-w-[120px]",
      render: (_, row: FolderListItem) => (
        <Text >
          {formatIsoDate(row.created_at, true)}
        </Text>
      ),
    }
  ], []);

  if (isLoading && folderListWithRoot.length === 1) {
    return (
      <div className="flex flex-col items-center justify-center py-8 space-y-4">
        <AiOutlineLoading3Quarters className="size-8 animate-spin" />
        <Text variant="body-md">Carregando pastas...</Text>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-8 space-y-4">
        <X className="size-8 text-primary" />
        <Text variant="body-md" className="text-center">
          Erro ao carregar pastas: {error.message}
        </Text>
        <Button
          size="sm"
          onClick={() => refetch()}
          className="uppercase"
        >
          Tentar novamente
        </Button>
      </div>
    );
  }

  if (folderListWithRoot.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-8 space-y-4">
        <FaFolder className="size-8 text-muted-foreground" />
        <Text variant="body-md" className="text-center">
          Nenhuma pasta disponível para mover a pasta
        </Text>
      </div>
    );
  }

  return (
    <div className="space-y-4 min-h-[300px]">
      <Text variant="body-md" className="mb-4">
        Selecione a pasta de destino para mover a pasta:
      </Text>

      <DataTable
        columns={columns}
        data={folderListWithRoot}
        keyField="folder_id"
        enableSelection
        singleSelection
        onSelectionChange={(selectedRows) => {
          const selectedFolder = selectedRows.length > 0 ? selectedRows[0] : null;
          setSelectedFolder(selectedFolder?.folder_id || null);
        }}
        className="border rounded-md"
        useFixedLayout={false}
        showHeaders={true}
        pagination={pagination && pagination.total_items > pagination.page_size ? {
          pageSize: pagination.page_size,
          totalItems: pagination.total_items,
          currentPage: pagination.current_page,
          onPageChange: setPage,
        } : undefined}
      />
    </div>
  );
}

interface MoveFolderDialogFooterProps {
  folderId: string;
  currentFolderId?: string | null;
  onMoveSuccess?: () => void;
}

export function MoveFolderDialogFooter({ folderId, currentFolderId, onMoveSuccess }: MoveFolderDialogFooterProps) {
  const selectedFolder = useSelectedFolder();
  const { closeDialog } = useDialogActions();
  const { clearSelectedFolder } = useFolderListActions();
  const { moveFolderToFolderMutation } = useFolderCRUD();

  const handleCancel = () => {
    clearSelectedFolder();
    closeDialog();
  };

  const handleMove = () => {
    if (!selectedFolder) {
      return;
    }

    const destinationFolderId = selectedFolder === "root" ? null : selectedFolder;

    moveFolderToFolderMutation.mutate({
      folderId,
      sourceFolderId: currentFolderId,
      destinationFolderId,
    }, {
      onSuccess: () => {
        if (onMoveSuccess) {
          onMoveSuccess();
        }
        clearSelectedFolder();
      }
    });
  };

  return (
    <div className="flex gap-3">
      <Button
        className="uppercase !bg-transparent"
        onClick={handleCancel}
        disabled={moveFolderToFolderMutation.isPending}
      >
        Cancelar
      </Button>
      <Button
        className="uppercase !bg-foreground !text-background !font-bold disabled:opacity-60"
        icon={<Check size={16} />}
        iconPosition="right"
        onClick={handleMove}
        disabled={!selectedFolder || moveFolderToFolderMutation.isPending}
        loading={moveFolderToFolderMutation.isPending}
      >
        Mover
      </Button>
    </div>
  );
}

export const MoveFolderDialog = {
  Content: MoveFolderDialogContent,
  Footer: MoveFolderDialogFooter,
};