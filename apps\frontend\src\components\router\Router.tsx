import { lazy } from "react";
import DefaultLayout from "~/layouts/DefaultLayout";
import LoginLayout from "~/layouts/LoginLayout";
import { Routes, Route } from "react-router";
import ProtectedRoute from "~/components/router/ProtectedRoute";
import PermissionGuard from "~/components/router/PermissionGuard";
import { Permission } from "~/helpers/permissions.helper";

const LoginPage = lazy(() => import("~/pages/login"));
const ReportListPage = lazy(() => import("~/pages/report/list"));
const ReportDetailsPage = lazy(() => import("~/pages/report/details"));
const AccountPage = lazy(() => import("~/pages/account"));
const NoPermissionPage = lazy(() => import("~/pages/no-permission"));

const Router = () => {
  return (
    <Routes>
      {/* Rotas públicas */}
      <Route element={<LoginLayout />}>
        <Route path="/login" element={<LoginPage />} />
      </Route>

      {/* Rotas protegidas por autenticação */}
      <Route element={<ProtectedRoute />}>
        <Route element={<DefaultLayout />}>
          {/* Página inicial - requer permissão para visualizar lista de relatórios */}
          <Route
            path="/"
            element={
              <PermissionGuard requiredPermission={Permission.VIEW_REPORT_LIST}>
                <ReportListPage />
              </PermissionGuard>
            }
          />

          {/* Pasta - requer permissão para visualizar lista de relatórios */}
          <Route
            path="/pasta/:folderId"
            element={
              <PermissionGuard requiredPermission={Permission.VIEW_REPORT_LIST}>
                <ReportListPage />
              </PermissionGuard>
            }
          />

          {/* Detalhes do relatório - requer permissão para visualizar detalhes */}
          <Route
            path="report/:type/:id"
            element={
              <PermissionGuard requiredPermission={Permission.VIEW_REPORT_DETAILS}>
                <ReportDetailsPage />
              </PermissionGuard>
            }
          />

          {/* Configurações da conta - sempre acessível para usuários autenticados */}
          <Route
            path="/conta/configuracoes"
            element={<AccountPage />}
          />

          {/* Página quando usuário não tem acesso a rota - sem permissão - sempre acessível para usuários autenticados */}
          <Route path="/sem-permissao" element={<NoPermissionPage />} />
        </Route>
      </Route>
    </Routes>
  );
};

export default Router;
